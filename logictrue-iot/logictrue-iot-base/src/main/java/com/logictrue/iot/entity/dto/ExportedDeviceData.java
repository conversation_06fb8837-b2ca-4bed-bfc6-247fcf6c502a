package com.logictrue.iot.entity.dto;

import com.logictrue.iot.entity.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 导出的设备数据结构化模型
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Data
@ApiModel(description = "导出的设备数据")
public class ExportedDeviceData {

    @ApiModelProperty(value = "导出元数据")
    private ExportMetadata metadata;

    @ApiModelProperty(value = "设备信息列表")
    private List<DeviceInfo> devices;

    @ApiModelProperty(value = "设备模板绑定信息列表")
    private List<DeviceTemplateBindingInfo> templateBindings;

    @ApiModelProperty(value = "Excel模板信息列表")
    private List<ExcelTemplateInfo> excelTemplates;

    @ApiModelProperty(value = "检测数据列表")
    private List<DetectionDataInfo> detectionData;

    /**
     * 导出元数据
     */
    @Data
    @ApiModel(description = "导出元数据")
    public static class ExportMetadata {
        @ApiModelProperty(value = "导出版本")
        private String version = "1.0";

        @ApiModelProperty(value = "导出时间")
        private LocalDateTime exportTime;

        @ApiModelProperty(value = "导出用户")
        private String exportUser;

        @ApiModelProperty(value = "时间范围开始")
        private LocalDateTime timeRangeStart;

        @ApiModelProperty(value = "时间范围结束")
        private LocalDateTime timeRangeEnd;

        @ApiModelProperty(value = "导出的设备编码列表")
        private Object deviceCodes; //可以是List<String>或者String("ALL_DEVICES")

        @ApiModelProperty(value = "导出选项")
        private ExportOptions options;
    }

    /**
     * 导出选项
     */
    @Data
    @ApiModel(description = "导出选项")
    public static class ExportOptions {
        @ApiModelProperty(value = "是否包含原始文件")
        private Boolean includeRawFiles;

        @ApiModelProperty(value = "是否包含解析数据")
        private Boolean includeParsedData;

        @ApiModelProperty(value = "是否包含设备模板信息")
        private Boolean includeTemplateInfo;
    }

    /**
     * 设备信息
     */
    @Data
    @ApiModel(description = "设备信息")
    public static class DeviceInfo {
        @ApiModelProperty(value = "设备编码")
        private String deviceCode;

        @ApiModelProperty(value = "设备名称")
        private String deviceName;

        @ApiModelProperty(value = "设备类型")
        private String deviceType;

        @ApiModelProperty(value = "设备IP")
        private String ip;

        @ApiModelProperty(value = "设备端口")
        private Integer port;

        @ApiModelProperty(value = "采集类型")
        private Integer collectType;

        @ApiModelProperty(value = "文件ID")
        private String fileId;

        @ApiModelProperty(value = "VNC IP")
        private String vncIp;

        @ApiModelProperty(value = "最后上线时间")
        private LocalDateTime lastTime;

        @ApiModelProperty(value = "方法名")
        private String methodName;

        @ApiModelProperty(value = "数采开启状态")
        private Integer saveStatus;

        @ApiModelProperty(value = "备注")
        private String deviceRemark;

        @ApiModelProperty(value = "设备型号")
        private String deviceModel;

        @ApiModelProperty(value = "责任人")
        private String deviceDuty;

        @ApiModelProperty(value = "设备有效期")
        private LocalDateTime deviceDate;

        @ApiModelProperty(value = "是否有监控")
        private Boolean isCamera;

        @ApiModelProperty(value = "监控账号")
        private String cameraUser;

        @ApiModelProperty(value = "监控密码")
        private String cameraPassword;

        @ApiModelProperty(value = "监控IP")
        private String cameraIp;

        @ApiModelProperty(value = "监控端口号")
        private Integer cameraPort;

        @ApiModelProperty(value = "解析方法")
        private String analysisType;
    }

    /**
     * 设备模板绑定信息
     */
    @Data
    @ApiModel(description = "设备模板绑定信息")
    public static class DeviceTemplateBindingInfo {
        @ApiModelProperty(value = "绑定ID")
        private Long id;

        @ApiModelProperty(value = "设备编码")
        private String deviceCode;

        @ApiModelProperty(value = "模板ID")
        private Long templateId;

        @ApiModelProperty(value = "绑定状态")
        private Integer status;

        @ApiModelProperty(value = "备注")
        private String remark;

        @ApiModelProperty(value = "创建时间")
        private LocalDateTime createTime;
    }

    /**
     * Excel模板信息
     */
    @Data
    @ApiModel(description = "Excel模板信息")
    public static class ExcelTemplateInfo {
        @ApiModelProperty(value = "模板ID")
        private Long id;

        @ApiModelProperty(value = "模板编码")
        private String templateCode;

        @ApiModelProperty(value = "模板名称")
        private String templateName;

        @ApiModelProperty(value = "模板描述")
        private String templateDescription;

        @ApiModelProperty(value = "模板配置JSON")
        private String templateConfig;

        @ApiModelProperty(value = "创建时间")
        private LocalDateTime createTime;
    }

    /**
     * 检测数据信息
     */
    @Data
    @ApiModel(description = "检测数据信息")
    public static class DetectionDataInfo {
        @ApiModelProperty(value = "检测数据主表信息")
        private DeviceDetectionData mainData;

        @ApiModelProperty(value = "基础字段列表")
        private List<DeviceDetectionBasicField> basicFields;

        @ApiModelProperty(value = "表格表头列表")
        private List<DeviceDetectionTableHeader> tableHeaders;

        @ApiModelProperty(value = "表格数据列表")
        private List<DeviceDetectionTableData> tableDataList;

        @ApiModelProperty(value = "解析日志列表")
        private List<DeviceDetectionParseLog> parseLogs;

        @ApiModelProperty(value = "原始文件信息")
        private RawFileInfo rawFileInfo;

        private String deviceCode;
    }

    /**
     * 原始文件信息
     */
    @Data
    @ApiModel(description = "原始文件信息")
    public static class RawFileInfo {
        @ApiModelProperty(value = "文件名")
        private String fileName;

        @ApiModelProperty(value = "文件路径")
        private String filePath;

        @ApiModelProperty(value = "文件大小")
        private Long fileSize;

        @ApiModelProperty(value = "文件在ZIP中的相对路径")
        private String zipRelativePath;
    }
}
