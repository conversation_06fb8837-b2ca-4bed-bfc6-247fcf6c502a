package com.logictrue.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.logictrue.iot.entity.DeviceExportLog;
import com.logictrue.iot.entity.dto.DeviceExportRequest;
import com.logictrue.iot.entity.dto.DeviceExportResult;

/**
 * 设备数据导出Service接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IDeviceDataExportService {

    /**
     * 开始导出设备数据
     *
     * @param request 导出请求参数
     * @return 导出任务ID
     */
    String startExport(DeviceExportRequest request);

    /**
     * 获取导出进度
     *
     * @param taskId 任务ID
     * @return 导出结果
     */
    DeviceExportResult getExportProgress(String taskId);

    /**
     * 获取导出文件下载路径
     *
     * @param taskId 任务ID
     * @return 文件下载路径
     */
    String getDownloadPath(String taskId);

    /**
     * 取消导出任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancelExport(String taskId);

    /**
     * 分页查询导出历史记录
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param taskId 任务ID（可选）
     * @param exportStatus 导出状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<DeviceExportLog> getExportHistory(int pageNum, int pageSize, String taskId, Integer exportStatus, String startTime, String endTime);

    /**
     * 清理过期的导出文件
     *
     * @param expireDays 过期天数
     * @return 清理的文件数量
     */
    int cleanupExpiredExports(int expireDays);
}
