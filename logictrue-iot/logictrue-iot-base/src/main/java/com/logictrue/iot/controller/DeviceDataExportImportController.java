package com.logictrue.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.logictrue.common.core.domain.R;
import com.logictrue.iot.entity.DeviceExportLog;
import com.logictrue.iot.entity.DeviceImportLog;
import com.logictrue.iot.entity.dto.*;
import com.logictrue.iot.service.IDeviceDataExportService;
import com.logictrue.iot.service.IDeviceDataImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 设备数据导出导入Controller
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/device/data")
@Api(value = "设备数据导出导入", tags = "设备数据导出导入")
public class DeviceDataExportImportController {

    @Autowired
    private IDeviceDataExportService exportService;

    @Autowired
    private IDeviceDataImportService importService;

    /**
     * 开始导出设备数据
     */
    @PostMapping("/export/start")
    @ApiOperation(value = "开始导出设备数据")
    public R<String> startExport(@Valid @RequestBody DeviceExportRequest request) {
        try {
            String taskId = exportService.startExport(request);
            return R.ok(taskId, "导出任务已启动");
        } catch (Exception e) {
            log.error("启动导出任务失败", e);
            return R.fail("启动导出任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取导出进度
     */
    @GetMapping("/export/progress/{taskId}")
    @ApiOperation(value = "获取导出进度")
    public R<DeviceExportResult> getExportProgress(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        try {
            DeviceExportResult result = exportService.getExportProgress(taskId);
            if (result == null) {
                return R.fail("导出任务不存在");
            }
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取导出进度失败", e);
            return R.fail("获取导出进度失败: " + e.getMessage());
        }
    }

    /**
     * 下载导出文件
     */
    @GetMapping("/export/download/{taskId}")
    @ApiOperation(value = "下载导出文件")
    public ResponseEntity<Resource> downloadExportFile(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        try {
            DeviceExportResult result = exportService.getExportProgress(taskId);
            if (result == null || !result.isSuccess()) {
                return ResponseEntity.notFound().build();
            }

            String filePath = result.getFilePath();
            if (!StringUtils.hasText(filePath)) {
                return ResponseEntity.notFound().build();
            }

            File file = new File(filePath);
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);
            String fileName = result.getFileName();
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName)
                    .body(resource);

        } catch (Exception e) {
            log.error("下载导出文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 取消导出任务
     */
    @PostMapping("/export/cancel/{taskId}")
    @ApiOperation(value = "取消导出任务")
    public R<Boolean> cancelExport(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        try {
            boolean success = exportService.cancelExport(taskId);
            return R.ok(success, success ? "导出任务已取消" : "取消导出任务失败");
        } catch (Exception e) {
            log.error("取消导出任务失败", e);
            return R.fail("取消导出任务失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询导出历史记录
     */
    @GetMapping("/export/history")
    @ApiOperation(value = "分页查询导出历史记录")
    public R<IPage<DeviceExportLog>> getExportHistory(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @ApiParam(value = "页大小", example = "20") @RequestParam(defaultValue = "20") int pageSize,
            @ApiParam(value = "任务ID") @RequestParam(required = false) String taskId,
            @ApiParam(value = "导出状态") @RequestParam(required = false) Integer exportStatus,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String endTime) {
        try {
            IPage<DeviceExportLog> result = exportService.getExportHistory(pageNum, pageSize, taskId, exportStatus, startTime, endTime);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取导出历史失败", e);
            return R.fail("获取导出历史失败: " + e.getMessage());
        }
    }

    /**
     * 上传导入文件
     */
    @PostMapping("/import/upload")
    @ApiOperation(value = "上传导入文件")
    public R<String> uploadImportFile(
            @ApiParam(value = "导入文件", required = true) @RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return R.fail("请选择要导入的文件");
            }

            String taskId = importService.uploadAndValidateFile(file);
            return R.ok(taskId, "文件上传成功，正在验证数据");
        } catch (Exception e) {
            log.error("上传导入文件失败", e);
            return R.fail("上传导入文件失败: " + e.getMessage());
        }
    }

    /**
     * 验证导入数据
     */
    @GetMapping("/import/validate/{taskId}")
    @ApiOperation(value = "验证导入数据")
    public R<DeviceImportResult> validateImportData(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        try {
            DeviceImportResult result = importService.validateImportData(taskId);
            if (result == null) {
                return R.fail("导入任务不存在");
            }
            return R.ok(result);
        } catch (Exception e) {
            log.error("验证导入数据失败", e);
            return R.fail("验证导入数据失败: " + e.getMessage());
        }
    }

    /**
     * 执行数据导入
     */
    @PostMapping("/import/execute")
    @ApiOperation(value = "执行数据导入")
    public R<DeviceImportResult> executeImport(@Valid @RequestBody DeviceImportRequest request) {
        try {
            DeviceImportResult result = importService.executeImport(request);
            return R.ok(result, "导入任务已启动");
        } catch (Exception e) {
            log.error("执行数据导入失败", e);
            return R.fail("执行数据导入失败: " + e.getMessage());
        }
    }

    /**
     * 获取导入进度
     */
    @GetMapping("/import/progress/{taskId}")
    @ApiOperation(value = "获取导入进度")
    public R<DeviceImportResult> getImportProgress(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        try {
            DeviceImportResult result = importService.getImportProgress(taskId);
            if (result == null) {
                return R.fail("导入任务不存在");
            }
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取导入进度失败", e);
            return R.fail("获取导入进度失败: " + e.getMessage());
        }
    }

    /**
     * 取消导入任务
     */
    @PostMapping("/import/cancel/{taskId}")
    @ApiOperation(value = "取消导入任务")
    public R<Boolean> cancelImport(
            @ApiParam(value = "任务ID", required = true) @PathVariable String taskId) {
        try {
            boolean success = importService.cancelImport(taskId);
            return R.ok(success, success ? "导入任务已取消" : "取消导入任务失败");
        } catch (Exception e) {
            log.error("取消导入任务失败", e);
            return R.fail("取消导入任务失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询导入历史记录
     */
    @GetMapping("/import/history")
    @ApiOperation(value = "分页查询导入历史记录")
    public R<IPage<DeviceImportLog>> getImportHistory(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @ApiParam(value = "页大小", example = "20") @RequestParam(defaultValue = "20") int pageSize,
            @ApiParam(value = "任务ID") @RequestParam(required = false) String taskId,
            @ApiParam(value = "导入状态") @RequestParam(required = false) Integer importStatus,
            @ApiParam(value = "开始时间") @RequestParam(required = false) String startTime,
            @ApiParam(value = "结束时间") @RequestParam(required = false) String endTime) {
        try {
            IPage<DeviceImportLog> result = importService.getImportHistory(pageNum, pageSize, taskId, importStatus, startTime, endTime);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取导入历史失败", e);
            return R.fail("获取导入历史失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期的导出文件
     */
    @PostMapping("/export/cleanup")
    @ApiOperation(value = "清理过期的导出文件")
    public R<Integer> cleanupExpiredExports(
            @ApiParam(value = "过期天数", example = "7") @RequestParam(defaultValue = "7") int expireDays) {
        try {
            int cleanedCount = exportService.cleanupExpiredExports(expireDays);
            return R.ok(cleanedCount, "清理完成，共清理 " + cleanedCount + " 个过期文件");
        } catch (Exception e) {
            log.error("清理过期导出文件失败", e);
            return R.fail("清理过期导出文件失败: " + e.getMessage());
        }
    }

    /**
     * 清理临时导入文件
     */
    @PostMapping("/import/cleanup")
    @ApiOperation(value = "清理临时导入文件")
    public R<Integer> cleanupTempImportFiles(
            @ApiParam(value = "过期天数", example = "3") @RequestParam(defaultValue = "3") int expireDays) {
        try {
            int cleanedCount = importService.cleanupTempImportFiles(expireDays);
            return R.ok(cleanedCount, "清理完成，共清理 " + cleanedCount + " 个临时文件");
        } catch (Exception e) {
            log.error("清理临时导入文件失败", e);
            return R.fail("清理临时导入文件失败: " + e.getMessage());
        }
    }
}
