package com.logictrue.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.logictrue.iot.entity.DeviceImportLog;
import com.logictrue.iot.entity.dto.DeviceImportRequest;
import com.logictrue.iot.entity.dto.DeviceImportResult;
import com.logictrue.iot.entity.dto.ExportedDeviceData;
import org.springframework.web.multipart.MultipartFile;

/**
 * 设备数据导入Service接口
 *
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface IDeviceDataImportService {

    /**
     * 上传并验证导入文件
     *
     * @param file 导入文件
     * @return 任务ID
     */
    String uploadAndValidateFile(MultipartFile file);

    /**
     * 验证导入数据
     *
     * @param taskId 任务ID
     * @return 验证结果
     */
    DeviceImportResult validateImportData(String taskId);

    /**
     * 执行数据导入
     *
     * @param request 导入请求参数
     * @return 导入结果
     */
    DeviceImportResult executeImport(DeviceImportRequest request);

    /**
     * 获取导入进度
     *
     * @param taskId 任务ID
     * @return 导入结果
     */
    DeviceImportResult getImportProgress(String taskId);

    /**
     * 取消导入任务
     *
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancelImport(String taskId);

    /**
     * 解析导入文件内容
     *
     * @param filePath 文件路径
     * @return 解析后的设备数据
     */
    ExportedDeviceData parseImportFile(String filePath);

    /**
     * 分页查询导入历史记录
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param taskId 任务ID（可选）
     * @param importStatus 导入状态（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<DeviceImportLog> getImportHistory(int pageNum, int pageSize, String taskId, Integer importStatus, String startTime, String endTime);

    /**
     * 清理临时导入文件
     *
     * @param expireDays 过期天数
     * @return 清理的文件数量
     */
    int cleanupTempImportFiles(int expireDays);
}
