<template>
  <el-dialog
    title="导出进度"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
  >
    <div v-if="progressData" class="progress-container">
      <!-- 基本信息 -->
      <div class="basic-info">
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="任务ID">{{ progressData.taskId }}</el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getStatusType(progressData.status)">
              {{ getStatusText(progressData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备数量">{{ progressData.deviceCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="数据记录数">{{ progressData.detectionDataCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDateTime(progressData.startTime) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(progressData.completeTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 进度条 -->
      <div class="progress-section">
        <div class="progress-header">
          <span class="progress-title">导出进度</span>
          <span class="progress-percentage">{{ progressData.progress || 0 }}%</span>
        </div>
        <el-progress
          :percentage="progressData.progress || 0"
          :status="getProgressStatus(progressData.status)"
          :stroke-width="20"
        />
        <div class="current-step">
          <i class="el-icon-loading" v-if="progressData.status === 0"></i>
          {{ progressData.currentStep || '准备中...' }}
        </div>
      </div>

      <!-- 统计信息 -->
      <div v-if="progressData.statistics" class="statistics-section">
        <h4>导出统计</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ progressData.statistics.deviceInfoCount || 0 }}</div>
              <div class="stat-label">设备信息</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ progressData.statistics.templateBindingCount || 0 }}</div>
              <div class="stat-label">模板绑定</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ formatNumber(progressData.statistics.basicFieldCount) }}</div>
              <div class="stat-label">基础字段</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 15px;">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ formatNumber(progressData.statistics.tableDataRowCount) }}</div>
              <div class="stat-label">表格数据行</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ formatNumber(progressData.statistics.parseLogCount) }}</div>
              <div class="stat-label">解析日志</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-value">{{ formatFileSize(progressData.statistics.rawFilesTotalSize) }}</div>
              <div class="stat-label">原始文件大小</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 文件信息 -->
      <div v-if="isSuccess(progressData)" class="file-info-section">
        <h4>导出文件</h4>
        <div class="file-info">
          <div class="file-item">
            <i class="el-icon-document"></i>
            <div class="file-details">
              <div class="file-name">{{ progressData.fileName }}</div>
              <div class="file-size">{{ formatFileSize(progressData.fileSize) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="isFailed(progressData)" class="error-section">
        <el-alert
          title="导出失败"
          :description="progressData.errorMessage"
          type="error"
          :closable="false"
          show-icon
        />
      </div>
    </div>

    <div v-else class="loading-container">
      <el-loading-spinner />
      <p>正在获取导出进度...</p>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="progressData && progressData.status === 0"
        type="danger"
        @click="cancelExport"
        :loading="cancelling"
      >
        取消导出
      </el-button>
      <el-button
        v-if="progressData && isSuccess(progressData)"
        type="primary"
        @click="downloadFile"
      >
        <i class="el-icon-download"></i>
        下载文件
      </el-button>
      <el-button @click="closeDialog">
        {{ progressData && isCompleted(progressData) ? '关闭' : '后台运行' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getExportProgress, cancelDeviceExport } from '@/api/device/export'

export default {
  name: 'ExportProgressDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      progressData: null,
      polling: null,
      cancelling: false
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
      if (newVal && this.taskId) {
        this.startPolling()
      } else {
        this.stopPolling()
      }
    },
    taskId(newVal) {
      if (newVal && this.visible) {
        this.startPolling()
      }
    },
    dialogVisible(newVal) {
      this.$emit('update:visible', newVal)
      if (!newVal) {
        this.stopPolling()
      }
    }
  },
  beforeDestroy() {
    this.stopPolling()
  },
  methods: {
    // 开始轮询进度
    startPolling() {
      console.log('开始轮询导出进度，任务ID:', this.taskId)
      this.stopPolling()
      this.getProgress()
      this.polling = setInterval(() => {
        this.getProgress()
      }, 2000) // 每2秒轮询一次
    },

    // 停止轮询
    stopPolling() {
      if (this.polling) {
        clearInterval(this.polling)
        this.polling = null
      }
    },

    // 获取进度
    async getProgress() {
      if (!this.taskId) return

      try {
        const response = await getExportProgress(this.taskId)
        if (response.code === 200) {
          this.progressData = response.data
          console.log('导出进度更新:', this.progressData)

          // 如果任务完成（成功或失败），停止轮询
          if (this.isCompleted(this.progressData)) {
            console.log('导出任务完成，停止轮询')
            this.stopPolling()
          }
        }
      } catch (error) {
        console.error('获取导出进度失败:', error)
        this.stopPolling()
      }
    },

    // 取消导出
    async cancelExport() {
      try {
        await this.$confirm('确定要取消导出任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.cancelling = true
        const response = await cancelDeviceExport(this.taskId)
        if (response.code === 200) {
          this.$message.success('导出任务已取消')
          this.closeDialog()
        } else {
          this.$message.error(response.msg || '取消导出任务失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消导出失败:', error)
          this.$message.error('取消导出失败')
        }
      } finally {
        this.cancelling = false
      }
    },

    // 下载文件
    downloadFile() {
      this.$emit('download', this.taskId)
    },

    // 关闭对话框
    closeDialog() {
      this.dialogVisible = false
      this.$emit('close')
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 进行中
        1: 'success', // 成功
        2: 'danger'   // 失败
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '成功',
        2: '失败'
      }
      return statusMap[status] || '未知'
    },

    // 获取进度条状态
    getProgressStatus(status) {
      if (status === 1) return 'success'
      if (status === 2) return 'exception'
      return null
    },

    // 格式化数字
    formatNumber(num) {
      if (!num) return '0'
      return num.toLocaleString()
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString()
    },

    // 检查是否导出完成
    isCompleted(progressData) {
      return progressData && progressData.status !== null && (progressData.status === 1 || progressData.status === 2)
    },

    // 检查是否导出成功
    isSuccess(progressData) {
      return progressData && progressData.status === 1
    },

    // 检查是否导出失败
    isFailed(progressData) {
      return progressData && progressData.status === 2
    }
  }
}
</script>

<style scoped>
.progress-container {
  padding: 10px 0;
}

.basic-info {
  margin-bottom: 20px;
}

.progress-section {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.progress-title {
  font-weight: bold;
  color: #303133;
}

.progress-percentage {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.current-step {
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.statistics-section {
  margin-bottom: 20px;
}

.statistics-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.file-info-section {
  margin-bottom: 20px;
}

.file-info-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.file-info {
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.file-item {
  display: flex;
  align-items: center;
}

.file-item i {
  font-size: 24px;
  color: #409eff;
  margin-right: 10px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.file-size {
  color: #606266;
  font-size: 14px;
}

.error-section {
  margin-bottom: 20px;
}

.loading-container {
  text-align: center;
  padding: 40px 0;
  color: #606266;
}

.dialog-footer {
  text-align: right;
}
</style>
