<template>
  <el-dialog
    title="导出详情"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="exportLog" class="detail-container">
      <!-- 基本信息 -->
      <div class="section">
        <h4 class="section-title">基本信息</h4>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="任务ID">{{ exportLog.taskId }}</el-descriptions-item>
          <el-descriptions-item label="导出状态">
            <el-tag :type="getStatusType(exportLog.exportStatus)">
              {{ getStatusText(exportLog.exportStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="设备数量">{{ exportLog.deviceCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="数据记录数">{{ formatNumber(exportLog.detectionDataCount) }}</el-descriptions-item>
          <el-descriptions-item label="原始文件数">{{ exportLog.rawFileCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(exportLog.fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="进度">{{ exportLog.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="当前步骤">{{ exportLog.currentStep || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 时间信息 -->
      <div class="section">
        <h4 class="section-title">时间信息</h4>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="数据时间范围开始">{{ formatDateTime(exportLog.timeRangeStart) }}</el-descriptions-item>
          <el-descriptions-item label="数据时间范围结束">{{ formatDateTime(exportLog.timeRangeEnd) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(exportLog.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(exportLog.completeTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 导出选项 -->
      <div class="section">
        <h4 class="section-title">导出选项</h4>
        <el-descriptions :column="3" size="small" border>
          <el-descriptions-item label="包含原始文件">
            <el-tag :type="exportLog.includeRawFiles ? 'success' : 'info'">
              {{ exportLog.includeRawFiles ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="包含解析数据">
            <el-tag :type="exportLog.includeParsedData ? 'success' : 'info'">
              {{ exportLog.includeParsedData ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="包含模板信息">
            <el-tag :type="exportLog.includeTemplateInfo ? 'success' : 'info'">
              {{ exportLog.includeTemplateInfo ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 设备列表 -->
      <div class="section">
        <h4 class="section-title">导出设备列表</h4>
        <div class="device-list">
          <el-tag
            v-for="deviceCode in deviceCodes"
            :key="deviceCode"
            style="margin: 2px;"
          >
            {{ deviceCode }}
          </el-tag>
        </div>
      </div>

      <!-- 文件信息 -->
      <div v-if="exportLog.exportStatus === 1" class="section">
        <h4 class="section-title">文件信息</h4>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="文件名">{{ exportLog.fileName }}</el-descriptions-item>
          <el-descriptions-item label="文件路径">{{ exportLog.filePath }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 错误信息 -->
      <div v-if="exportLog.exportStatus === 2 && exportLog.errorMessage" class="section">
        <h4 class="section-title">错误信息</h4>
        <el-alert
          :title="exportLog.errorMessage"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 备注 -->
      <div v-if="exportLog.remark" class="section">
        <h4 class="section-title">备注</h4>
        <p class="remark-text">{{ exportLog.remark }}</p>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ExportDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    exportLog: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: this.visible
    }
  },
  computed: {
    deviceCodes() {
      if (!this.exportLog || !this.exportLog.deviceCodes) {
        return []
      }
      try {
        return JSON.parse(this.exportLog.deviceCodes)
      } catch (error) {
        console.error('解析设备编码列表失败:', error)
        return []
      }
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
    },
    dialogVisible(newVal) {
      this.$emit('update:visible', newVal)
    }
  },
  methods: {
    // 关闭对话框
    closeDialog() {
      this.dialogVisible = false
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 进行中
        1: 'success', // 成功
        2: 'danger'   // 失败
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '成功',
        2: '失败'
      }
      return statusMap[status] || '未知'
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 格式化数字
    formatNumber(num) {
      if (!num) return '0'
      return num.toLocaleString()
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString()
    }
  }
}
</script>

<style scoped>
.detail-container {
  max-height: 600px;
  overflow-y: auto;
}

.section {
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  border-left: 3px solid #409eff;
  padding-left: 10px;
}

.device-list {
  max-height: 120px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.remark-text {
  margin: 0;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
  color: #606266;
  line-height: 1.5;
}
</style>
