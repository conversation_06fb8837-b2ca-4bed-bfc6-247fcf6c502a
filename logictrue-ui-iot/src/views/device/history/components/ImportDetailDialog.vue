<template>
  <el-dialog
    title="导入详情"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
  >
    <div v-if="importLog" class="detail-container">
      <!-- 基本信息 -->
      <div class="section">
        <h4 class="section-title">基本信息</h4>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="任务ID">{{ importLog.taskId }}</el-descriptions-item>
          <el-descriptions-item label="导入状态">
            <el-tag :type="getStatusType(importLog.importStatus)">
              {{ getStatusText(importLog.importStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="文件名">{{ importLog.fileName }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(importLog.fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="进度">{{ importLog.progress || 0 }}%</el-descriptions-item>
          <el-descriptions-item label="当前步骤">{{ importLog.currentStep || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 导入统计 -->
      <div class="section">
        <h4 class="section-title">导入统计</h4>
        <el-descriptions :column="3" size="small" border>
          <el-descriptions-item label="导入设备数">{{ importLog.importedDeviceCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="跳过设备数">{{ importLog.skippedDeviceCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="失败设备数">{{ importLog.failedDeviceCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="导入数据记录数">{{ formatNumber(importLog.importedDetectionDataCount) }}</el-descriptions-item>
          <el-descriptions-item label="导入原始文件数">{{ importLog.importedRawFileCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="成功率">{{ calculateSuccessRate() }}%</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 时间信息 -->
      <div class="section">
        <h4 class="section-title">时间信息</h4>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="创建时间">{{ formatDateTime(importLog.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="完成时间">{{ formatDateTime(importLog.completeTime) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 导入选项 -->
      <div class="section">
        <h4 class="section-title">导入选项</h4>
        <el-descriptions :column="2" size="small" border>
          <el-descriptions-item label="导入模式">{{ getImportModeText(importLog.importMode) }}</el-descriptions-item>
          <el-descriptions-item label="冲突策略">{{ getConflictStrategyText(importLog.conflictStrategy) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 成功设备列表 -->
      <div v-if="successDeviceCodes.length > 0" class="section">
        <h4 class="section-title">成功导入设备 ({{ successDeviceCodes.length }})</h4>
        <div class="device-list">
          <el-tag
            v-for="deviceCode in successDeviceCodes"
            :key="deviceCode"
            type="success"
            style="margin: 2px;"
          >
            {{ deviceCode }}
          </el-tag>
        </div>
      </div>

      <!-- 失败设备列表 -->
      <div v-if="failedDeviceCodes.length > 0" class="section">
        <h4 class="section-title">失败设备 ({{ failedDeviceCodes.length }})</h4>
        <div class="device-list">
          <el-tag
            v-for="deviceCode in failedDeviceCodes"
            :key="deviceCode"
            type="danger"
            style="margin: 2px;"
          >
            {{ deviceCode }}
          </el-tag>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="importLog.importStatus === 2 && importLog.errorMessage" class="section">
        <h4 class="section-title">错误信息</h4>
        <el-alert
          :title="importLog.errorMessage"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 备注 -->
      <div v-if="importLog.remark" class="section">
        <h4 class="section-title">备注</h4>
        <p class="remark-text">{{ importLog.remark }}</p>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ImportDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    importLog: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: this.visible
    }
  },
  computed: {
    successDeviceCodes() {
      if (!this.importLog || !this.importLog.successDeviceCodes) {
        return []
      }
      try {
        return JSON.parse(this.importLog.successDeviceCodes)
      } catch (error) {
        console.error('解析成功设备编码列表失败:', error)
        return []
      }
    },
    failedDeviceCodes() {
      if (!this.importLog || !this.importLog.failedDeviceCodes) {
        return []
      }
      try {
        return JSON.parse(this.importLog.failedDeviceCodes)
      } catch (error) {
        console.error('解析失败设备编码列表失败:', error)
        return []
      }
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
    },
    dialogVisible(newVal) {
      this.$emit('update:visible', newVal)
    }
  },
  methods: {
    // 关闭对话框
    closeDialog() {
      this.dialogVisible = false
    },

    // 计算成功率
    calculateSuccessRate() {
      if (!this.importLog) return 0
      const total = (this.importLog.importedDeviceCount || 0) + 
                   (this.importLog.skippedDeviceCount || 0) + 
                   (this.importLog.failedDeviceCount || 0)
      if (total === 0) return 0
      const success = this.importLog.importedDeviceCount || 0
      return Math.round((success / total) * 100)
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 进行中
        1: 'success', // 成功
        2: 'danger',  // 失败
        3: 'warning'  // 部分成功
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '成功',
        2: '失败',
        3: '部分成功'
      }
      return statusMap[status] || '未知'
    },

    // 获取导入模式文本
    getImportModeText(mode) {
      const modeMap = {
        'REPLACE': '替换模式',
        'APPEND': '追加模式',
        'UPDATE': '更新模式'
      }
      return modeMap[mode] || mode || '-'
    },

    // 获取冲突策略文本
    getConflictStrategyText(strategy) {
      const strategyMap = {
        'OVERRIDE': '覆盖',
        'SKIP': '跳过',
        'ERROR': '报错'
      }
      return strategyMap[strategy] || strategy || '-'
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 格式化数字
    formatNumber(num) {
      if (!num) return '0'
      return num.toLocaleString()
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString()
    }
  }
}
</script>

<style scoped>
.detail-container {
  max-height: 600px;
  overflow-y: auto;
}

.section {
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  border-left: 3px solid #409eff;
  padding-left: 10px;
}

.device-list {
  max-height: 120px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.remark-text {
  margin: 0;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
  color: #606266;
  line-height: 1.5;
}
</style>
