import request from '@/utils/request'

/**
 * 上传导入文件
 * @param {FormData} formData 文件数据
 */
export function uploadImportFile(formData) {
  return request({
    url: '/iot/device/data/import/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 验证导入数据
 * @param {String} taskId 任务ID
 */
export function validateImportData(taskId) {
  return request({
    url: `/iot/device/data/import/validate/${taskId}`,
    method: 'get'
  })
}

/**
 * 执行数据导入
 * @param {Object} data 导入请求参数
 */
export function executeImport(data) {
  return request({
    url: '/iot/device/data/import/execute',
    method: 'post',
    data: data
  })
}

/**
 * 获取导入进度
 * @param {String} taskId 任务ID
 */
export function getImportProgress(taskId) {
  return request({
    url: `/iot/device/data/import/progress/${taskId}`,
    method: 'get'
  })
}

/**
 * 取消导入任务
 * @param {String} taskId 任务ID
 */
export function cancelDeviceImport(taskId) {
  return request({
    url: `/iot/device/data/import/cancel/${taskId}`,
    method: 'post'
  })
}

/**
 * 获取导入历史记录
 * @param {Object} query 查询参数
 */
export function getImportHistory(query) {
  return request({
    url: '/iot/device/data/import/history',
    method: 'get',
    params: query
  })
}

/**
 * 清理临时导入文件
 * @param {Number} expireDays 过期天数
 */
export function cleanupTempImportFiles(expireDays = 3) {
  return request({
    url: '/iot/device/data/import/cleanup',
    method: 'post',
    params: { expireDays }
  })
}
